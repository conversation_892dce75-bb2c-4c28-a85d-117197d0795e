# frozen_string_literal: true

class CreateConsultationRequestSystem < ActiveRecord::Migration[7.1]
  disable_ddl_transaction!
  tag :predeploy

  def change
    # Faculty Time Slots table
    create_table :faculty_time_slots do |t|
      t.references :user, null: false, foreign_key: true, index: true
      t.datetime :start_time, null: false
      t.datetime :end_time, null: false
      t.string :day_of_week, null: false, limit: 10
      t.boolean :is_recurring, default: true, null: false
      t.date :specific_date, null: true
      t.boolean :is_available, default: true, null: false
      t.text :notes
      t.timestamps null: false

      t.index [:user_id, :day_of_week, :start_time], name: 'index_faculty_time_slots_on_user_day_time'
      t.index [:user_id, :specific_date], name: 'index_faculty_time_slots_on_user_specific_date'
      t.index [:is_available, :start_time], name: 'index_faculty_time_slots_on_availability'
    end

    # Consultation Requests table
    create_table :consultation_requests do |t|
      t.references :student, null: false, foreign_key: { to_table: :users }, index: true
      t.references :faculty, null: false, foreign_key: { to_table: :users }, index: true
      t.references :faculty_time_slot, null: false, foreign_key: true, index: true
      t.string :student_name, null: false, limit: 255
      t.string :student_number, null: false, limit: 50
      t.datetime :preferred_datetime, null: false
      t.text :description, null: false
      t.string :nature_of_concern, null: false, limit: 50
      t.string :status, default: 'pending', null: false, limit: 20
      t.text :faculty_comment
      t.datetime :approved_at
      t.datetime :declined_at
      t.datetime :completed_at
      t.references :approved_by, null: true, foreign_key: { to_table: :users }
      t.timestamps null: false

      t.index [:student_number, :status], name: 'index_consultation_requests_on_student_status'
      t.index [:faculty_id, :status], name: 'index_consultation_requests_on_faculty_status'
      t.index [:status, :preferred_datetime], name: 'index_consultation_requests_on_status_datetime'
      t.index [:nature_of_concern, :status], name: 'index_consultation_requests_on_concern_status'
    end

    # Consultation Summaries table (for completed consultations)
    create_table :consultation_summaries do |t|
      t.references :consultation_request, null: false, foreign_key: true, index: true
      t.references :faculty, null: false, foreign_key: { to_table: :users }, index: true
      t.references :student, null: false, foreign_key: { to_table: :users }, index: true
      t.string :student_name, null: false, limit: 255
      t.string :student_number, null: false, limit: 50
      t.datetime :consultation_date, null: false
      t.string :concern_type, null: false, limit: 50
      t.text :description
      t.text :faculty_notes
      t.text :outcome_summary
      t.string :referral_made, limit: 255
      t.text :follow_up_required
      t.timestamps null: false

      t.index [:faculty_id, :concern_type], name: 'index_consultation_summaries_on_faculty_concern'
      t.index [:student_number, :consultation_date], name: 'index_consultation_summaries_on_student_date'
      t.index [:concern_type, :consultation_date], name: 'index_consultation_summaries_on_concern_date'
    end

    # Add check constraints for valid enum values
    add_check_constraint :consultation_requests, 
      "nature_of_concern IN ('Personal', 'Academic', 'Teacher-related', 'Co-Students', 'Family', 'Other')",
      name: 'consultation_requests_nature_of_concern_check'

    add_check_constraint :consultation_requests,
      "status IN ('pending', 'approved', 'declined', 'completed', 'cancelled')",
      name: 'consultation_requests_status_check'

    add_check_constraint :faculty_time_slots,
      "day_of_week IN ('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')",
      name: 'faculty_time_slots_day_of_week_check'

    add_check_constraint :consultation_summaries,
      "concern_type IN ('Personal', 'Academic', 'Teacher-related', 'Co-Students', 'Family', 'Other')",
      name: 'consultation_summaries_concern_type_check'

    # Add time validation constraints
    add_check_constraint :faculty_time_slots,
      "end_time > start_time",
      name: 'faculty_time_slots_time_order_check'

    add_check_constraint :consultation_requests,
      "preferred_datetime > created_at",
      name: 'consultation_requests_future_datetime_check'
  end
end
